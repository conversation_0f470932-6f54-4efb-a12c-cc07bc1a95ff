{"SALES_FILE": "data/input/DE_Prep_HB_EDA_data1.xlsx", "CLUSTER_FILE": "data/input/cluster_data.xlsx", "COVER_FILE": "data/input/cover_data.xlsx", "DEPTH_FILE": "data/input/depth_data.xlsx", "OUTPUT_FILE": "data/output/Final_df_with_feature_imp_perf.xlsx", "REFERENCE_OUTPUT_FILE": "data/output/Final_df_with_feature_imp_perf_reference_period.xlsx", "GROUP_FILTER": "HOMEWARE", "OPTIMIZATION_ON": "REVENUE", "REFERENCE_MONTHS": [202501, 202502], "WEIGHTS": {"cover": 0.15, "gmv_margin_pct": 0.25, "gmv_per_lm": 0.25, "sub_cpen": 0.1, "d_units": 0.15, "asp": 0.1}, "PERFORM_CLUSTER_LEVEL_ANALYSIS": true, "INPUT_FILE": "data/output/Final_df_with_feature_imp_perf.xlsx", "OUTPUT_DIR": "data/output", "MIN_DATA_POINTS": 3, "MID_COVER_START": 15, "HIGH_COVER_START": 60, "PENALTY_MULTIPLIER_HIGH_COVER": 0.3, "LOW_PERCENTILE_THRESHOLD": 0.4, "PENALTY_MULTIPLIER_LOW_PERFORMING": 0.5, "locations": ["29072", "29020"], "CLUSTER_DROP_LOC_CD": [29001], "DEFAULT_OPTIMIZATION_CONFIG": {"min_increase_pct": 0.1, "max_increase_pct": 0.6, "min_decrease_pct": 0.1, "max_decrease_pct": 0.35, "slack_penalty": 800, "more_cover_days": 90, "less_cover_days": 30, "adjustment_step_pct": 0.25, "max_iterations": 800, "top_n_to_increase": 15, "top_n_to_decrease": 7}, "OVERRIDE_DICT": {}, "DEPTH_DICT": {}, "DEFAULT_DEPTH": 4, "PERFORMANCE_FILE": "data/output/Final_df_with_feature_imp_perf_reference_period.xlsx", "SATURATION_FILE": {"overall": "data/output/saturation_output_overall.xlsx", "cluster": "data/output/saturation_output_cluster{x}.xlsx"}, "PLOT_DATA_FILE": {"overall": "data/output/plot_data/plot_data_overall.xlsx", "cluster": "data/output/plot_data/plot_data_cluster{x}.xlsx"}, "DATABASE_CONFIG": {"ENABLED": true, "RESULT_TABLES": {"SALES_SOH_CUSTOMER_DATA": "app_hb_preopt", "COVER_DEPTH_DATA": "cover_depth_hb", "MAIN_OUTPUT": "overall_performance_data_hb", "REFERENCE_PERIOD_DATA": "reference_period_data_hb", "INVALID_DATA_CNSTRNT_VIL": "invalid_data_constraint_vltn", "ADJUSTED_DATA": "adjusted_data_hb", "SATURATION_DATA": "saturation_results_hb", "PLOT_DATA": "plot_data_results_hb"}, "DEFAULT_METADATA": {"pipeline_version": "1.0", "run_timestamp": "CURRENT_TIMESTAMP", "config_group_filter": "{GROUP_FILTER}", "config_optimization_on": "{OPTIMIZATION_ON}"}}, "DB_TYPE": "mysql", "DB_HOST": "localhost", "DB_PORT": 3306, "DB_USER": "prakhar", "DB_PASSWORD": "landmark489", "DB_NAME": "space_optimization_db", "TABLE_WRITE_MODE": "append"}