import pandas as pd
import numpy as np

def normalize(series):
    return (series - series.min()) / (series.max() - series.min())

def calculate_weeks_between(start_date, end_date):
    delta_days = (pd.to_datetime(end_date) - pd.to_datetime(start_date)).days
    return round(delta_days / 7)

def filter_reference_period(df, reference_months, output_file):
    start_month, end_month = reference_months
    print('start_month',start_month)
    print('end_month',end_month)
    filtered_df = df[(df['MONTH'] >= start_month) & (df['MONTH'] <= end_month)]
    # filtered_df.to_excel(output_file, index=False)
    return filtered_df
