df = db.read("sales_table", filters={"StoreID": 101})  # Equal (=)

df = db.read("sales_table", filters={"StoreID": ("!=", 101)})  # Not Equal (!=)

df = db.read("sales_table", filters={"Quantity": (">", 500)})  # Greater Than (>)

df = db.read("sales_table", filters={"Quantity": (">=", 500)})  # Greater Than or Equal (>=)

df = db.read("sales_table", filters={"Quantity": ("<", 100)})  # Less Than (<)

df = db.read("sales_table", filters={"Quantity": ("<=", 100)})  # Less Than or Equal (<=)

df = db.read("sales_table", filters={"Quantity": ("between", 100, 500)})  # BETWEEN

df = db.read("sales_table", filters={"StoreID": ("in", [101, 102, 103])})  # IN List

df = db.read("sales_table", filters={"StoreName": ("like", "%Central%")})  # LIKE

df = db.read("sales_table", filters={"StoreID": 101, "Quantity": (">", 500)})  # AND conditions

df = db.read("sales_table", or_filters=[{"StoreID": 101}, {"StoreID": 102}])  # OR conditions

df = db.read("sales_table", filters={"Quantity": (">", 500)}, or_filters=[{"StoreID": 101}, {"StoreID": 102}])  # AND + OR

df = db.read("sales_table", latest_by="created_at", latest_only=True)  # Latest single record

df = db.read("sales_table", latest_by="created_at")  # All rows sorted latest first

df = db.read(
    "sales_table",
    filters={"Quantity": ("between", 100, 500), "Status": "Active"},
    or_filters=[{"StoreID": 101}, {"StoreID": 102}],
    latest_by="created_at"
)  # Complex: BETWEEN + AND + OR + Latest


df = db.read(
    "sales_table",
    filters={
        "YearStart": (">=", 2025),
        "YearEnd": ("<=", 2024)
    }
)
