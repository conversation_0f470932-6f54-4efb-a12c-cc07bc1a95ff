apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: azure-space-fastapi-secret-provider
spec:
  provider: azure
  parameters:
    usePodIdentity: "false"
    useVMManagedIdentity: "true"
    userAssignedIdentityID: "93734389-ee8e-4cbd-93b3-bbf4667b641f"
    keyvaultName: "LMAPAZ1KYVDLLPRD01"
    objects: |
      array:
        - |
          objectName: space-dev-aap-user
          objectType: secret

    tenantId: "1f9b09b4-197c-4f1c-b0c5-571a6ccc96c8 "
  secretObjects:
    - secretName: space-dev-db
      type: Opaque
      data:
        - objectName: space-dev-aap-user
          key: space-dev-db-key
    