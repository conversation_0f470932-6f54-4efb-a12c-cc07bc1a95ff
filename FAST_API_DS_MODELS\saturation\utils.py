import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_pdf import PdfPages
from scipy.optimize import root_scalar
from pathlib import Path
import re
from db_writer import DBWriter
import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from shared_config import get_config
cfg = get_config()
# ----------------------------------------
# General Helpers
# ----------------------------------------

def poly_func(x, *coeffs):
    return sum(c * x**i for i, c in enumerate(coeffs))

def find_input_given_output(func, target_output, x_range):
    def objective(x):
        return func(x) - target_output
    f_a, f_b = objective(x_range[0]), objective(x_range[1])
    if f_a * f_b > 0:
        return None
    result = root_scalar(objective, bracket=x_range, method='brentq')
    return result.root if result.converged else None

def sanitize_filename(name):
    return re.sub(r'[\\/*?:"<>|]', "_", name)

def initialize_pdf(pdf_path):
    return PdfPages(pdf_path)

# ----------------------------------------
# Plotting Functions
# ----------------------------------------

def plot_gaussian_analysis(grp_nm, dpt_nm, clss_nm, Subclass,
                           gauss_range, y_pred, X_gauss, y,
                           refined_sat, sat_lm, perf_at_sat, density,
                           save_path=None, return_fig=False):
    fig, ax1 = plt.subplots(figsize=(10, 4))

    ax1.plot(gauss_range, y_pred, label='Polynomial Fit', color='blue')
    ax1.scatter(X_gauss, y, alpha=0.5, label='Actual Points')
    ax1.axvline(refined_sat, color='red', linestyle='--', label='Saturation')

    ax1.annotate(f"Sat LM: {sat_lm:.2f}\nPerf: {perf_at_sat:.2f}",
                 xy=(refined_sat, perf_at_sat),
                 xytext=(refined_sat + 0.1, perf_at_sat),
                 arrowprops=dict(arrowstyle="->", color='red'),
                 fontsize=9, color='red')

    title = (f"GRP_NM: {grp_nm} | DPT_NM: {dpt_nm} | CLSS_NM: {clss_nm}\n"
             f"SUB_CLSS_NM: {Subclass} - Gaussian Normalized")
    ax1.set_title(title)
    ax1.set_xlabel('Normalized LM')
    ax1.set_ylabel('Performance')
    ax1.legend(loc='upper left')

    ax2 = ax1.twinx()
    ax2.plot(gauss_range, density, color='green', linestyle=':', label='Density')
    ax2.set_ylabel('Density')

    fig.tight_layout()

    if save_path:
        fig.savefig(save_path, bbox_inches='tight')

    if return_fig:
        return fig
    plt.close(fig)

def plot_actual_values_with_saturation(grp_nm, dpt_nm, clss_nm, Subclass,
                                       X_raw, y, qt, coeffs,
                                       sat_lm, perf_at_sat,
                                       save_path=None, return_fig=False):
    fig, ax = plt.subplots(figsize=(10, 4))

    X_gauss = qt.transform(X_raw.reshape(-1, 1))
    x_range = np.linspace(X_gauss.min(), X_gauss.max(), 200)
    y_pred = poly_func(x_range, *coeffs)
    x_range_raw = qt.inverse_transform(x_range.reshape(-1, 1))

    ax.plot(x_range_raw, y_pred, label='Polynomial Fit', color='blue')
    ax.scatter(X_raw, y, alpha=0.5, label='Actual Points')
    ax.axvline(sat_lm, color='red', linestyle='--', label='Saturation')

    ax.annotate(f"Sat LM: {sat_lm:.2f}\nPerf: {perf_at_sat:.2f}",
                xy=(sat_lm, perf_at_sat),
                xytext=(sat_lm + 0.1, perf_at_sat),
                arrowprops=dict(arrowstyle="->", color='red'),
                fontsize=9, color='red')

    title = (f"GRP_NM: {grp_nm} | DPT_NM: {dpt_nm} | CLSS_NM: {clss_nm}\n"
             f"SUB_CLSS_NM: {Subclass} - Actual Linear Meter")
    ax.set_title(title)
    ax.set_xlabel('Linear Meter')
    ax.set_ylabel('Performance')
    ax.legend()

    fig.tight_layout()

    if save_path:
        fig.savefig(save_path, bbox_inches='tight')

    if return_fig:
        return fig
    plt.close(fig)

def plot_combined_subplot(grp_nm, dpt_nm, clss_nm, subclass,
                          gauss_range, y_pred, x_gauss, y, refined_sat, sat_lm, perf_at_sat,
                          density, x_raw, qt, best_coeffs,
                          return_fig=False):
    fig, axes = plt.subplots(1, 2, figsize=(14, 5))
    fig.suptitle(f"GRP_NM: {grp_nm} | DPT_NM: {dpt_nm} | CLSS_NM: {clss_nm} | SUB_CLSS_NM: {subclass}", fontsize=10)

    axes[0].plot(gauss_range, y_pred, label='Polynomial Fit', color='blue')
    axes[0].scatter(x_gauss, y, alpha=0.3, label='Actual')
    axes[0].plot(gauss_range, density * max(y), label='Density (scaled)', linestyle='--', color='gray')
    axes[0].axvline(refined_sat, color='red', linestyle='--', label='Saturation Point')
    axes[0].set_title("Gaussian Space")
    axes[0].set_xlabel("Quantile Transformed Linear Meter")
    axes[0].set_ylabel("Performance")
    axes[0].legend()
    axes[0].grid(True)

    lm_space = np.linspace(min(x_raw), max(x_raw), 200).reshape(-1, 1)
    y_space = poly_func(qt.transform(lm_space).flatten(), *best_coeffs)

    axes[1].scatter(x_raw, y, alpha=0.3, label='Actual')
    axes[1].plot(lm_space, y_space, color='blue', label='Polynomial Fit')
    axes[1].axvline(sat_lm, color='red', linestyle='--', label=f'Saturation = {sat_lm:.1f}')
    axes[1].axhline(perf_at_sat, color='green', linestyle='--', label=f'Perf at Sat = {perf_at_sat:.2f}')
    axes[1].set_title("Linear Meter Space")
    axes[1].set_xlabel("Linear Meter")
    axes[1].set_ylabel("Performance")
    axes[1].legend()
    axes[1].grid(True)

    plt.tight_layout(rect=[0, 0.03, 1, 0.95])

    if return_fig:
        return fig
    else:
        plt.show()

# ----------------------------------------
# Export Utilities
# ----------------------------------------
# def convert_all_arrays_to_lists(df):
#     for col in df.columns:
#         if df[col].apply(lambda x: isinstance(x, (np.ndarray, list, tuple))).any():
#             df[col] = df[col].apply(lambda x: list(x) if isinstance(x, (np.ndarray, tuple)) else x)
#     return df
import json
def convert_all_arrays_to_lists(df):
    for col in df.columns:
        if df[col].apply(lambda x: isinstance(x, (np.ndarray, list, tuple))).any():
            df[col] = df[col].apply(
                lambda x: json.dumps(list(x)) if isinstance(x, (np.ndarray, list, tuple)) else x
            )
    return df

# def save_plot_data_to_excel(plot_data_rows, cluster_name, plot_data_config):
#     df_plot = pd.DataFrame(plot_data_rows)
#     df_plot['Cluster'] = cluster_name

#     if isinstance(plot_data_config, dict):
#         template_path = plot_data_config.get("cluster" if cluster_name != "overall" else "overall")
#     else:
#         template_path = plot_data_config

#     if "{x}" in template_path:
#         output_path = template_path.replace("{x}", sanitize_filename(str(cluster_name)))
#     else:
#         output_path = template_path

#     pd_path = Path(output_path)
#     pd_path.parent.mkdir(parents=True, exist_ok=True)

#     df_plot.to_excel(output_path, index=False)
#     writer = DBWriter(cfg)
#     writer.write(df_plot, "PLOT_DATA")

def save_plot_data_to_excel(plot_data_rows, cluster_name, plot_data_config):
    df_plot = pd.DataFrame(plot_data_rows)
    df_plot['Cluster'] = cluster_name

    if isinstance(plot_data_config, dict):
        template_path = plot_data_config.get("cluster" if cluster_name != "overall" else "overall")
    else:
        template_path = plot_data_config

    if "{x}" in template_path:
        output_path = template_path.replace("{x}", sanitize_filename(str(cluster_name)))
    else:
        output_path = template_path

    # pd_path = Path(output_path)
    # pd_path.parent.mkdir(parents=True, exist_ok=True)

    # df_plot.to_excel(output_path, index=False)

    saturation_data_df = convert_all_arrays_to_lists(df_plot)
    writer = DBWriter(cfg)
    writer.write(saturation_data_df, "PLOT_DATA")


def resolve_template_path(template_config, cluster_name):
    if isinstance(template_config, dict):
        template = template_config.get("cluster" if cluster_name != "overall" else "overall")
    else:
        template = template_config

    if "{x}" in template:
        return template.replace("{x}", str(cluster_name))
    
    return template

def convert_arrays_to_lists(row):
    for col in ['x_raw', 'x_gauss', 'y', 'y_pred', 'density']:
        if isinstance(row[col], np.ndarray):
            row[col] = row[col].tolist()
        elif isinstance(row[col], (list, tuple)):
            row[col] = list(row[col])
    return row
