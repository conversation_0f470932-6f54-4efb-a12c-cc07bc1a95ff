import json
import os
from functools import lru_cache

CONFIG_PATH = os.path.abspath(os.path.join(os.path.dirname(__file__), "shared_config.json"))

@lru_cache()
def get_config():
    """Loads and caches the configuration from shared_config.json."""
    with open(CONFIG_PATH, 'r') as f:
        return json.load(f)

def update_config(new_config: dict):
    """Updates the shared_config.json file with new values."""
    with open(CONFIG_PATH, 'r') as f:
        config = json.load(f)

    config.update(new_config)

    with open(CONFIG_PATH, 'w') as f:
        json.dump(config, f, indent=4)

    # Clear the cache so get_config reflects new values
    get_config.cache_clear()
