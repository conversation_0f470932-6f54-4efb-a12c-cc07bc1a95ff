import pandas as pd
import numpy as np
from sklearn.preprocessing import MinMaxScaler
from typing import List, Dict, Tu<PERSON>, Optional
from gurobipy import Model, GRB, quicksum
import sys
import os

# Add project root to Python path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from shared_config import get_config

cfg = get_config()

from db_writer import DBWriter

# cfg = get_config()
optimization_metric = cfg.get("OPTIMIZATION_ON", "GMV").upper()

def load_retail_data(performance_file: str = cfg["PERFORMANCE_FILE"],cluster_file: str = cfg["CLUSTER_FILE"],depth_file: str = cfg["DEPTH_FILE"]) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Load and prepare input data files based on the cluster of input locations.
    Automatically selects the appropriate cluster-level saturation file.
    """
    try:
        # Load performance data
        # data_new = pd.read_excel(performance_file, engine='openpyxl')
        # data_new['LOC_CD'] = data_new['LOC_CD'].astype(str)

        reference_period_table = cfg["DATABASE_CONFIG"]["RESULT_TABLES"]["REFERENCE_PERIOD_DATA"]
        writer = DBWriter(cfg)
        data_new = writer.read(        
        table_name=reference_period_table,
        # latest_by="created_at",
        latest_only=True)

        data_new['LOC_CD'] = data_new['LOC_CD'].astype(str)

        locations = cfg["locations"]
        locations = [str(loc) for loc in locations]

        matched = data_new[data_new['LOC_CD'].isin(locations)]

        # ***************************Depth data upload*********************
        # depth_map = pd.read_excel(depth_file)
        # depth_map['LOC_CD'] = depth_map['LOC_CD'].astype(str)

        depth_map_table = cfg["DATABASE_CONFIG"]["RESULT_TABLES"]["COVER_DEPTH_DATA"]
        writer = DBWriter(cfg)
        depth_map = writer.read(        
        table_name=depth_map_table,
        # latest_by="created_at",
        latest_only=True)

        depth_map['LOC_CD'] = depth_map['LOC_CD'].astype(str)

        data_new = data_new.merge(depth_map,on=['LOC_CD','GRP_NM', 'DPT_NM', 'CLSS_NM','SUB_CLSS_NM'],how='left')

        if matched.empty:
            raise ValueError(f"No cluster mapping found for the locations: {locations}")

        # clusters = matched['CLUSTER_NUM'].unique()
        # if len(clusters) > 1:
        #     raise ValueError(f"Multiple clusters found for given locations: {clusters}. Please provide locations from a single cluster.")
        clusters = matched['CLUSTER_NUM'].dropna().unique()
        if len(clusters) > 1:
            raise ValueError(f"Multiple clusters found: {clusters}")
        elif len(clusters) == 0:
            raise ValueError(f"No cluster mapping found for: {locations}")
        cluster_id = int(clusters[0])

        cluster_id = int(clusters[0])
        print('Your cluster id is : ',cluster_id)

        # Build the correct saturation file path
        saturation_path_template = cfg["SATURATION_FILE"]["cluster"]
        saturation_file = saturation_path_template.replace("{x}", str(cluster_id))

        # Load saturation data
        # data_sat = pd.read_excel(saturation_file, engine="openpyxl")
        saturation_data_table = cfg["DATABASE_CONFIG"]["RESULT_TABLES"]["SATURATION_DATA"]
        writer = DBWriter(cfg)
        data_sat = writer.read(        
        table_name=saturation_data_table,
        # latest_by="created_at",
        latest_only=True,
        filters={"Cluster": cluster_id})

        data_sat = data_sat[['GRP_NM', 'DPT_NM', 'CLSS_NM', 'SUB_CLSS_NM', 'max_saturation_point']]
        data_sat = data_sat.rename(columns={'max_saturation_point': 'max_sat_lm'})

        # Optionally filter performance data
        data_new = data_new[data_new['LOC_CD'].isin(locations)].copy()

        data_new = data_new[[
            'GRP_NM', 'DPT_NM', 'CLSS_NM', 'SUB_CLSS_NM', 'LOC_CD',
            'MONTH', 'TOTAL_LM', 'MIN_LM', 'GMV_PER_LM',
            'GMV', 'NET_SLS_AMT','D_UNITS', 'MARGIN_PERC', 'CUST_PEN', 'ASP', 'COVER_INVERSE', 'Performance','MNTH_AVG_OPTN_CNT','DEPTH'
        ]]

        return data_new, data_sat

    except Exception as e:
        raise RuntimeError(f"Error loading retail data: {str(e)}")

def prepare_location_data(
    data_new: pd.DataFrame, 
    data_sat: pd.DataFrame, 
    loc_cd: str, 
    include_invalid: bool = False
) -> Optional[Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]]:
    """Prepare data for a specific location with robust validation."""
    try:
        # Filter to location
        data_new = data_new[data_new['LOC_CD'] == loc_cd].copy()
        # Compute current cover
        data_new['COVER_INVERSE'] = pd.to_numeric(data_new['COVER_INVERSE'], errors='coerce') 
        data_new['current_cover_in_days'] = np.where(
            data_new['COVER_INVERSE'] > 0,
            1 / data_new['COVER_INVERSE'] * 7,
            np.nan
        )
        print("data_new",data_new.shape)
        data_new1 = data_new[[
            'GRP_NM', 'DPT_NM', 'CLSS_NM', 'SUB_CLSS_NM', 'LOC_CD', 
            'MONTH', 'TOTAL_LM', 'MIN_LM', 'current_cover_in_days',
            'GMV','NET_SLS_AMT' ,'Performance','GMV_PER_LM','DEPTH','MNTH_AVG_OPTN_CNT'
        ]]

        print("data_new : Min and max Dates of final dataframe used for optimization: ",data_new1['MONTH'].min(),data_new1['MONTH'].max())
        # Step 1: Find latest month data per group
        latest_info = (
            data_new1.sort_values('MONTH', ascending=False)
            .groupby(['GRP_NM', 'DPT_NM', 'CLSS_NM', 'SUB_CLSS_NM', 'LOC_CD'], as_index=False)
            .first()[[
                'GRP_NM', 'DPT_NM', 'CLSS_NM', 'SUB_CLSS_NM', 'LOC_CD', 'MONTH',
                'TOTAL_LM', 'MIN_LM', 'current_cover_in_days','GMV_PER_LM','GMV','NET_SLS_AMT','DEPTH'
            ]]
        )

        # Step 2: Aggregate over all months
        agg_all_months = data_new1.groupby(
            ['GRP_NM', 'DPT_NM', 'CLSS_NM', 'SUB_CLSS_NM', 'LOC_CD'], as_index=False
        ).agg(
            Performance=('Performance', 'mean'),
            GMV_sum_reference_month=('GMV', 'sum'),
            NET_SLS_AMT_sum_reference_month=('NET_SLS_AMT', 'sum'),
            TOTAL_LM_AVG=('TOTAL_LM', 'mean'),
            AVG_OPTN_CNT=('MNTH_AVG_OPTN_CNT', 'mean')
        )

        # Step 3: Merge both sets
        df_all_data = pd.merge(
            latest_info, agg_all_months,
            on=['GRP_NM', 'DPT_NM', 'CLSS_NM', 'SUB_CLSS_NM', 'LOC_CD'], how='left'
        )

        # Rename for clarity
        # df_all_data = df_all_data.rename(columns={'TOTAL_LM': 'current_lm'})
        # df_all_data = df_all_data.rename(columns={'TOTAL_LM_AVG': 'current_lm'})
        df_all_data['OPTION_DENSITY'] = df_all_data['AVG_OPTN_CNT']/df_all_data['TOTAL_LM_AVG']
        df_all_data['current_lm'] = df_all_data['TOTAL_LM_AVG']
        df_all_data['current_lm'] = pd.to_numeric(df_all_data['current_lm'], errors='coerce').fillna(0)

        # Merge with saturation table
        df_loc = df_all_data.merge(
            data_sat,
            on=['GRP_NM', 'DPT_NM', 'CLSS_NM', 'SUB_CLSS_NM'],
            how='left'
        )

        df_loc['max_sat_lm'] = pd.to_numeric(df_loc['max_sat_lm'], errors='coerce')
        df_loc['MIN_LM'] = pd.to_numeric(df_loc['MIN_LM'], errors='coerce').fillna(0)
        df_loc['GMV_per_linear_meter_ref_months'] = df_loc['GMV_sum_reference_month']/df_loc['current_lm']
        df_loc['NET_SLS_AMT_per_linear_meter_ref_months'] = df_loc['NET_SLS_AMT_sum_reference_month']/df_loc['current_lm']

        print("Min and max Dates of final dataframe used for optimization: \n",df_loc['MONTH'].min(),df_loc['MONTH'].max())

        # Split valid and invalid subclasses
        valid_df, invalid_df = handle_invalid_subclasses(df_loc, include_invalid)

        if not invalid_df.empty:
            print(f"Excluded {len(invalid_df)} subclasses where MIN_LM > max_sat_lm \n")
            # os.makedirs(cfg['OUTPUT_DIR'], exist_ok=True)
            # invalid_df.to_excel(f"{cfg['OUTPUT_DIR']}/excluded_subclasses_min_lm_greater_than_max_sat_lm_{loc_cd}.xlsx", index=False)
            # writer = DBWriter(cfg)
            # writer.write(invalid_df, "INVALID_DATA_MIN_GRT_MAX")

        # Validate columns
        required_cols = ['SUB_CLSS_NM', 'current_lm', 'MIN_LM', 'max_sat_lm', 'GMV']
        missing_cols = [col for col in required_cols if col not in valid_df.columns]

        if missing_cols:
            print(f"Missing required columns: {missing_cols} \n")
            return None

        return valid_df, invalid_df, df_loc

    except Exception as e:
        print(f"Error preparing data for {loc_cd}: {str(e)}")
        return None

def handle_invalid_subclasses(df: pd.DataFrame, include_invalid: bool) -> Tuple[pd.DataFrame, pd.DataFrame]:
    invalid_subclasses = df[df['MIN_LM'] > df['max_sat_lm']].copy()
    
    if include_invalid:
        # Modify min_lm for invalid subclasses
        df.loc[df['MIN_LM'] > df['max_sat_lm'], 'MIN_LM'] = df[['current_lm', 'max_sat_lm']].min(axis=1)
        return df, invalid_subclasses
    else:
        # Filter out invalid subclasses (original behavior)
        valid_df = df[df['MIN_LM'] <= df['max_sat_lm']].copy()
        return valid_df, invalid_subclasses

def classify_subclasses_into_quadrants(df: pd.DataFrame, cover_cutoffs: dict = None) -> pd.DataFrame:
    if cover_cutoffs is None:
        cover_cutoffs = {}

    DEFAULT_MID_COVER_START =int(cfg.get('MID_COVER_START', 15)) #15
    DEFAULT_HIGH_COVER_START = int(cfg.get('HIGH_COVER_START',60)) #60

    applied_cutoffs = []

    def apply_cover_penalty(row):
        subclass = row['SUB_CLSS_NM']
        MID_COVER_START = cover_cutoffs.get(subclass, {}).get('MID_COVER_START', DEFAULT_MID_COVER_START)
        HIGH_COVER_START = cover_cutoffs.get(subclass, {}).get('HIGH_COVER_START', DEFAULT_HIGH_COVER_START)
        
        applied_cutoffs.append({
            'LOC_CD': row.get('LOC_CD', 'UNKNOWN'),
            'SUB_CLSS_NM': subclass,
            'MID_COVER_START': MID_COVER_START,
            'HIGH_COVER_START': HIGH_COVER_START
        })

        if row['current_cover_in_days'] > HIGH_COVER_START:
            return 0.6
        elif row['current_cover_in_days'] > MID_COVER_START:
            return 0.2
        else:
            return 0
    print('LOC_CD data type',df['LOC_CD'].dtype)
    # df = df.dropna(subset=['Performance'],axis=1)   ####dropping na values
    df['cover_penalty'] = df.apply(apply_cover_penalty, axis=1)
    df['adjusted_performance'] = df['Performance'] * (1 - df['cover_penalty'])

    df['perf_bucket'] = pd.qcut(df['adjusted_performance'], q=3, labels=['L', 'M', 'H'])
    df['lm_bucket'] = pd.qcut(df['current_lm'], q=3, labels=['L', 'M', 'H'])

    cover_info_df = pd.DataFrame(applied_cutoffs).drop_duplicates()
    print('LOC_CD data type',cover_info_df['LOC_CD'].dtype)
    # print("\n=== MID/HIGH Cover Thresholds Applied ===")
    # print(cover_info_df.to_string(index=False))

    def get_cover_bucket(row):
        subclass = row['SUB_CLSS_NM']
        MID_COVER_START = cover_cutoffs.get(subclass, {}).get('MID_COVER_START', DEFAULT_MID_COVER_START)
        HIGH_COVER_START = cover_cutoffs.get(subclass, {}).get('HIGH_COVER_START', DEFAULT_HIGH_COVER_START)
        
        if row['current_cover_in_days'] > HIGH_COVER_START:
            return 'H'
        elif row['current_cover_in_days'] > MID_COVER_START:
            return 'M'
        else:
            return 'L'
    
    df['cover_bucket'] = df.apply(get_cover_bucket, axis=1)

    action_rules = {
        ('L', 'L', 'L'): 'increase',
        ('L', 'L', 'M'): 'keep',
        ('L', 'L', 'H'): 'decrease',
        ('L', 'M', 'L'): 'increase',
        ('L', 'M', 'M'): 'decrease',
        ('L', 'M', 'H'): 'decrease',
        ('L', 'H', 'L'): 'keep',
        ('L', 'H', 'M'): 'decrease',
        ('L', 'H', 'H'): 'decrease',

        ('M', 'L', 'L'): 'increase',
        ('M', 'L', 'M'): 'increase',
        ('M', 'L', 'H'): 'decrease',
        ('M', 'M', 'L'): 'increase',
        ('M', 'M', 'M'): 'keep',
        ('M', 'M', 'H'): 'decrease',
        ('M', 'H', 'L'): 'keep',
        ('M', 'H', 'M'): 'decrease',
        ('M', 'H', 'H'): 'decrease',

        ('H', 'L', 'L'): 'increase',
        ('H', 'L', 'M'): 'increase',
        ('H', 'L', 'H'): 'decrease',
        ('H', 'M', 'L'): 'increase',
        ('H', 'M', 'M'): 'increase',
        ('H', 'M', 'H'): 'decrease',
        ('H', 'H', 'L'): 'increase',
        ('H', 'H', 'M'): 'keep',
        ('H', 'H', 'H'): 'decrease',
    }

    df['action'] = df.apply(
        lambda row: action_rules.get(
            (row['perf_bucket'], row['lm_bucket'], row['cover_bucket']), 'keep'
        ),
        axis=1
    )

    return df

def calculate_and_apply_bounds(df_input,loc_cd):
    df = df_input.copy()
    df['current_lm'] = np.round(df['current_lm'],2)

    conditions = [
        df['action'] == 'increase',
        df['action'] == 'decrease',
        df['action'] == 'keep'
    ]
    
    lb_choices = [
        round(df['current_lm'] * 1.1, 2),
        round(np.maximum(df['current_lm'] * 0.7, df['MIN_LM']), 2),
        0
    ]

    ub_choices = [
        round(np.minimum(df['current_lm'] * 1.5,df['max_sat_lm']), 2),
        round(df['current_lm'] * 0.9, 2),
        0
    ]

    df['lb'] = np.select(conditions, lb_choices, default=np.nan)
    df['ub'] = np.select(conditions, ub_choices, default=np.nan)
    
    invalid_df = df[df['lb'] > df['ub']].copy()
    valid_df = df.drop(invalid_df.index).copy()

    # os.makedirs(cfg['OUTPUT_DIR'], exist_ok=True)
    # output_file = f"{cfg['OUTPUT_DIR']}/excluded_subclasses_lb_greater_than_ub_{loc_cd}.xlsx"
    # invalid_df.to_excel(f"{cfg['OUTPUT_DIR']}/excluded_subclasses_{loc_cd}.xlsx", index=False)
    # invalid_df.to_excel(f"{cfg['OUTPUT_DIR']}/excluded_subclasses_lb_greater_than_ub_{loc_cd}.xlsx", index=False)
    
    writer = DBWriter(cfg)
    writer.write(invalid_df, "INVALID_DATA_CNSTRNT_VIL")
    # invalid_df.to_excel(output_file, index=False)

    return valid_df, invalid_df

def optimize_lm_space(valid_df_x, cfg=None, override_dict=None,optimization_metric=optimization_metric ):
    """Optimize LM space allocation with cover-aware objective, bounds, and manual override support."""
    cfg = cfg or {}
    
    try:
        required_cols = ['current_lm', 'action', 'SUB_CLSS_NM', 'GMV', 'max_sat_lm', 'MIN_LM', 'current_cover_in_days','GMV_sum_reference_month','GMV_per_linear_meter_ref_months','NET_SLS_AMT_per_linear_meter_ref_months','NET_SLS_AMT_sum_reference_month']
        missing = [col for col in required_cols if col not in valid_df_x.columns]
        if missing:
            raise ValueError(f"Missing columns: {missing}")

        valid_df_x = valid_df_x.dropna(subset=required_cols).copy()
        valid_df_x['status'] = 'Not touched'

        # Apply overrides if provided
        if override_dict:
            for subclass, fixed_lm in override_dict.items():
                if subclass in valid_df_x['SUB_CLSS_NM'].values:
                    valid_df_x.loc[valid_df_x['SUB_CLSS_NM'] == subclass, 'current_lm'] = fixed_lm
                    valid_df_x.loc[valid_df_x['SUB_CLSS_NM'] == subclass, 'action'] = 'keep'
                    valid_df_x.loc[valid_df_x['SUB_CLSS_NM'] == subclass, 'status'] = 'Overridden'
                    print(f" Manually overriding {subclass}: Setting current_lm to {fixed_lm:.2f} \n")

        valid_df_x['action'] = valid_df_x['action'].apply(lambda x: x if x in ['keep', 'increase', 'decrease'] else 'keep')
        valid_df_x['current_lm'] = np.round(valid_df_x['current_lm'], 2)

        dfs = {
            'keep': valid_df_x[valid_df_x['action'] == 'keep'].copy(),
            'increase': valid_df_x[(valid_df_x['action'] == 'increase') & 
                                   (valid_df_x['current_lm'] < valid_df_x['max_sat_lm']) & 
                                   (valid_df_x['max_sat_lm'] > 0) &
                                   (valid_df_x['status'] != 'Overridden')].copy(),
            'decrease': valid_df_x[(valid_df_x['action'] == 'decrease') & 
                                   (valid_df_x['current_lm'] > valid_df_x['MIN_LM']) & 
                                   (valid_df_x['MIN_LM'] >= 0) &
                                   (valid_df_x['status'] != 'Overridden')].copy()
        }

        optimize_space = dfs['increase']['current_lm'].sum() + dfs['decrease']['current_lm'].sum()
        print(f"Space to optimize: {optimize_space:.2f}")
        m = Model("LM_Optimization")
        m.setParam("OutputFlag", 1)

        variables = {}
        variable_metadata = {}

        for action in ['increase', 'decrease']:
            for idx, row in dfs[action].iterrows():
                name = str(row['SUB_CLSS_NM']).replace(' ', '_')
                current = row['current_lm']
                cover = row['current_cover_in_days']

                try:
                    if action == 'increase':
                        increase_pct = cfg['max_increase_pct']
                        if cover < cfg['less_cover_days']:
                            # ub = round(current * (1 + cfg['max_increase_pct']), 2)
                            ub = round(min(current * (1 + increase_pct), row['max_sat_lm']), 2)
                        else:
                            ub = round(min(current * (1 + increase_pct), row['max_sat_lm']), 2)
                        lb = round(current * (1 + cfg['min_increase_pct']), 2)
                        
                    else:
                        decrease_pct = cfg['max_decrease_pct']
                        ub = round(current * (1 - cfg['min_decrease_pct']), 2)
                        if cover > cfg['more_cover_days']:
                            # lb = round(row['min_lm'], 2)
                            lb = round(max(current * (1 - decrease_pct), row['MIN_LM']), 2)
                        else:
                            lb = round(max(current * (1 - decrease_pct), row['MIN_LM']), 2)

                    if lb >= ub:
                        print(f"\n Skipping {name}: invalid bounds ({lb:.2f} >= {ub:.2f}) ")
                        continue

                    var = m.addVar(lb=lb, ub=ub, vtype=GRB.CONTINUOUS, name=f"lm_{name}")
                    variables[name] = var
                    variable_metadata[name] = row

                    # print(f" {name}: {current:.2f} -> [{lb:.2f}, {ub:.2f}] | Cover: {cover}d | Action: {action}")

                except Exception as e:
                    print(f" Error creating variable {name}: {str(e)}")

        if not variables:
            raise ValueError("No valid variables created for optimization")

        slack_pos = m.addVar(name="slack_pos")
        slack_neg = m.addVar(name="slack_neg")
        m.addConstr(quicksum(variables.values()) + slack_pos - slack_neg == optimize_space, "space_constraint")

        # optimization_metric = cfg["OPTIMIZATION_ON"].upper()
        # optimization_metric = cfg.get("OPTIMIZATION_ON", "GMV").upper()
        
        print("Optimization is on :",optimization_metric)

        metric_col = "GMV_sum_reference_month" if optimization_metric == "GMV" else "NET_SLS_AMT_sum_reference_month"


        # obj = quicksum(
        #         (variable_metadata[name]['GMV_sum_reference_month'] / variable_metadata[name]['current_lm']) * var
        #         for name, var in variables.items()
        #     )

        obj = quicksum(
                (variable_metadata[name][metric_col] / variable_metadata[name]['current_lm']) * var
                for name, var in variables.items()
            )

        m.setObjective(obj - cfg['slack_penalty'] * (slack_pos + slack_neg), GRB.MAXIMIZE)

        print("=== SOLVING MODEL ===\n")
        m.optimize()

        if m.status != GRB.OPTIMAL:
            raise RuntimeError(f"Optimization failed with status {m.status}")

        results = []
        for name, var in variables.items():
            row = variable_metadata[name]
            results.append({
                **row.to_dict(),
                'optimized_lm': var.X,
                'lm_delta': var.X - row['current_lm'],
                'space_change_%': (var.X - row['current_lm']) / row['current_lm'] * 100,
                'change': 'Increase' if row['action'] == 'increase' else 'Decrease'
            })

        dfs['keep']['optimized_lm'] = dfs['keep']['current_lm']
        dfs['keep']['lm_delta'] = 0
        dfs['keep']['change'] = 'Keep'
        dfs['keep']['space_change_%'] = 0.0
        dfs['keep']['status'] = 'Not touched'

        result_df = pd.concat([dfs['keep'], pd.DataFrame(results)], ignore_index=True)

        if override_dict:
            for subclass in override_dict.keys():
                result_df.loc[result_df['SUB_CLSS_NM'] == subclass, 'status'] = 'Overridden'

        optimized_sum = result_df[result_df['action'].isin(['increase', 'decrease'])]['optimized_lm'].sum()
        deviation = (optimized_sum - optimize_space) / optimize_space * 100
        print(f"Optimized space: {optimized_sum:.2f} (Deviation: {deviation:.2f}%)")

        return result_df

    except Exception as e:
        print(f"\n Optimization failed: {str(e)}")
        if 'm' in locals():
            try:
                m.computeIIS()
                m.write("model.ilp")
                print("Infeasible constraints written to model.ilp")
            except Exception as ie:
                print(f"Couldn't write IIS: {str(ie)}")
        return None

def adjust_to_match_space_by_cover(result_df, original_total, cfg=None):
    """
    Iteratively adjust optimized_lm to eliminate space deviation using cover and performance score.
    - Prioritizes low cover + high performance for increase.
    - Prioritizes high cover + low performance for decrease.
    - Tracks detailed adjustment strings.
    """

    cfg = cfg or {}
    df = result_df.copy()
    df['optimized_lm_before_devi_adjust'] = df['optimized_lm']
    df['adjustment'] = 'No change'

    def get_deviation(df):
        return df[df['change'].isin(['Increase', 'Decrease'])]['optimized_lm'].sum() - original_total

    deviation = get_deviation(df)
    print(f"Deviation before adjustment: {deviation:.2f} (Target: {original_total:.2f})")

    if abs(deviation) < 1e-2:
        df['adjustment'] = 'No change needed'
        return df

    step_pct = cfg['adjustment_step_pct']
    max_iters = cfg['max_iterations']
    iters = 0

    while abs(deviation) > 1e-2 and iters < max_iters:
        if deviation > 0:
            # Over-allocated → Reduce space from subclasses with high cover and poor performance
            eligible = df[(df['change'] == 'Decrease') & (df['current_cover_in_days'] > cfg['more_cover_days'])]
            eligible = eligible.sort_values(by=['current_cover_in_days', 'Performance'], ascending=[False, True])
            eligible = eligible.head(cfg['top_n_to_decrease'])

        else:
            # Under-allocated → Add space to subclasses with low cover and high performance
            eligible = df[(df['change'] == 'Increase') & (df['current_cover_in_days'] < cfg['less_cover_days'])]
            eligible = eligible.sort_values(by=['current_cover_in_days', 'Performance'], ascending=[True, False])
            eligible = eligible.head(cfg['top_n_to_increase'])

        if eligible.empty:
            print(" No eligible rows for cover/performance-based adjustment.")
            df['adjustment'] = 'No eligible rows for deviation fix'
            break

        for idx, row in eligible.iterrows():
            current_lm = df.at[idx, 'optimized_lm']
            base_lm = row['current_lm']
            min_lm = row['MIN_LM']
            max_step = round(base_lm * step_pct, 2)
            remaining_dev = abs(deviation)

            step = min(max_step, remaining_dev)

            if deviation > 0:
                # Reduce
                new_val = max(current_lm - step, min_lm)
                actual_step = current_lm - new_val
                if actual_step > 0:
                    pct = (actual_step / base_lm) * 100
                    df.at[idx, 'optimized_lm'] = round(new_val, 2)
                    prev_adj = df.at[idx, 'adjustment']
                    df.at[idx, 'adjustment'] = (
                        prev_adj if prev_adj == 'No change' else prev_adj + " | "
                    ) + f"-{pct:.1f}% (-{actual_step:.2f} LM)"
                    deviation -= actual_step

            else:
                # Increase
                new_val = current_lm + step
                actual_step = step
                pct = (actual_step / base_lm) * 100
                df.at[idx, 'optimized_lm'] = round(new_val, 2)
                prev_adj = df.at[idx, 'adjustment']
                df.at[idx, 'adjustment'] = (
                    prev_adj if prev_adj == 'No change' else prev_adj + " | "
                ) + f"+{pct:.1f}% (+{actual_step:.2f} LM)"
                deviation += actual_step

            if abs(deviation) <= 1e-2:
                break

        iters += 1

    df['lm_delta'] = df['optimized_lm'] - df['current_lm']
    df['space_change_%'] = (df['lm_delta'] / df['current_lm']) * 100
    final_dev = get_deviation(df)
    print(f" Final deviation after adjustment: {final_dev:.2f} in {iters} iterations")
    df['Final_Action'] = np.where(
        df['lm_delta'] > 0, 'Increase',
        np.where(df['lm_delta'] < 0, 'Decrease', 'Keep')
    )
    return df

def calculate_gmv_metrics(adjusted_df, data_new,optimization_metric=optimization_metric ):
    """Calculate GMV or Revenue metrics and return updated dataframe with metrics."""

    print("All calculation happening on matric:",optimization_metric)
    metric_col_total = "GMV_sum_reference_month" if optimization_metric == "GMV" else "NET_SLS_AMT_sum_reference_month"
    metric_col_per_lm = "GMV_per_linear_meter_ref_months" if optimization_metric == "GMV" else "NET_SLS_AMT_per_linear_meter_ref_months"

    # 1. Calculate new value (GMV or Revenue)
    adjusted_df["new_metric"] = adjusted_df.apply(
        lambda row: (
            0.9 * row[metric_col_per_lm] * row["optimized_lm"] if row["change"] == "Increase"
            else 1.1 * row[metric_col_per_lm] * row["optimized_lm"] if row["change"] == "Decrease"
            else row[metric_col_total]
        ),
        axis=1)
    
    adjusted_df['optimized_no_of_options'] = adjusted_df['OPTION_DENSITY'] * adjusted_df["optimized_lm"]
    adjusted_df['optimized_qty'] = adjusted_df['optimized_no_of_options'] * adjusted_df["DEPTH"]

    # 2. Total lift calculation
    current_total = adjusted_df[metric_col_total].sum()
    new_total = adjusted_df['new_metric'].sum()
    lift = new_total - current_total
    pct_lift = (lift / current_total) * 100 if current_total != 0 else 0

    print(f"\n--- {optimization_metric} Summary ---")
    print(f"Current {optimization_metric}: {current_total:,.2f}")
    print(f"New {optimization_metric}: {new_total:,.2f}")
    print(f"Lift in {optimization_metric}: {lift:,.2f}")
    print(f"% Lift in {optimization_metric}: {pct_lift:.2f}%")

    # 3. Daily metrics
    data_new['MONTH_CONV'] = pd.to_datetime(data_new['MONTH'].astype(str), format='%Y%m')

    location_dates = data_new.groupby('LOC_CD')['MONTH_CONV'].agg(['min', 'max']).reset_index()
    location_dates['min'] = location_dates['min'].apply(lambda x: x.replace(day=1))
    location_dates['max'] = location_dates['max'].apply(lambda x: x.replace(day=1) + pd.DateOffset(months=1) - pd.Timedelta(days=1))
    location_dates['days_btw_reference_period'] = (location_dates['max'] - location_dates['min']).dt.days
    location_dates['days_btw_reference_period'] = location_dates['days_btw_reference_period'].apply(lambda x: x if x > 0 else 30)
    location_dates = location_dates.drop(['min', 'max'], axis=1)

    adjusted_df = adjusted_df.merge(location_dates, on='LOC_CD', how='left')
    adjusted_df['current_per_day_metric'] = np.round(adjusted_df[metric_col_total] / adjusted_df['days_btw_reference_period'], 2)
    adjusted_df['current_per_day_metric_per_lm'] = adjusted_df['current_per_day_metric'] / adjusted_df['current_lm']

    adjusted_df["new_metric_per_day"] = adjusted_df.apply(
        lambda row: (
            0.9 * row["current_per_day_metric_per_lm"] * row["optimized_lm"] if row["change"] == "Increase"
            else 1.1 * row["current_per_day_metric_per_lm"] * row["optimized_lm"] if row["change"] == "Decrease"
            else row["current_per_day_metric"]
        ),
        axis=1)

    # 4. Daily lift
    current_daily = adjusted_df['current_per_day_metric'].sum()
    new_daily = adjusted_df['new_metric_per_day'].sum()
    daily_lift = new_daily - current_daily
    daily_pct_lift = (daily_lift / current_daily) * 100 if current_daily != 0 else 0

    print(f"\n--- Daily {optimization_metric} Summary ---")
    print(f"Current Daily {optimization_metric}: {current_daily:,.2f}")
    print(f"New Daily {optimization_metric}: {new_daily:,.2f}")
    print(f"Lift in Daily {optimization_metric}: {daily_lift:,.2f}")
    print(f"% Lift in Daily {optimization_metric}: {daily_pct_lift:.2f}%")

    return adjusted_df

def save_results(adjusted_df, loc_cd):
    """Save the results to output directory with location code in filename."""
    # os.makedirs(cfg['OUTPUT_DIR'], exist_ok=True)
    # output_file = f"{cfg['OUTPUT_DIR']}/optimized_results_{loc_cd}.xlsx"
    # adjusted_df.to_excel(output_file, index=False)
    writer = DBWriter(cfg)
    writer.write(adjusted_df, "ADJUSTED_DATA")
    # print(f"\n Results saved to {output_file}")