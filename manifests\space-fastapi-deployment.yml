apiVersion: apps/v1
kind: Deployment
metadata:
  name: space-fastapi-backend
  namespace: dll-space-optimization-ml
spec:
  replicas: 1
  selector:
    matchLabels:
      app: space-fastapi-backend
  template:
    metadata:
      labels:
        app: space-fastapi-backend
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: agentpool
                operator: In
                values: 
                - usrnodepl01
      tolerations:
        - key: "a"
          operator: "Equal"
          value: "b"
          effect: "NoSchedule"
      containers:
        - name: space-fastapi-backend
          image: lmapaz1acrdllprd02.azurecr.io/space/space-fastapi-backend-img
          ports:
            - containerPort: 8000
          env:
            - name: ENDPOINT
              valueFrom:
                configMapKeyRef:
                  name: space-fastapi-app-config
                  key: ENDPOINT
            
            - name: DB_USER
              valueFrom:
                configMapKeyRef:
                  name: space-fastapi-app-config
                  key: DB_USER
            
            - name: DB_NAME
              valueFrom:
                configMapKeyRef:
                  name: space-fastapi-app-config
                  key: DB_NAME

            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: space-dev-db
                  key: space-dev-db-key
           
            - name: ROOT_PATH
              valueFrom:
                configMapKeyRef:
                  name: space-fastapi-app-config
                  key: ROOT_PATH
    

          volumeMounts:
             - name: secrets-volume
               mountPath: "/mnt/secrets-store"
               readOnly: true
      volumes:
        - name: config-volume
          configMap:
            name: space-fastapi-app-config
        - name: secrets-volume
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: "azure-space-fastapi-secret-provider"