from fastapi import FastAP<PERSON>, Body
from fastapi.responses import JSONResponse
from typing import Dict, Any
import subprocess
import sys
import os

from shared_config import get_config, update_config

app = FastAPI()

VALID_MODULES = ["datapreparation", "saturation", "optimization"]


@app.get("/")
def root():
    return {
        "message": "Welcome to the Optimizer API",
        "routes": [
            {"GET": "/get-config"},
            {"POST": "/run-all"},
            {"POST": "/run/{module_name} (e.g. /run/datapreparation)"},
        ]
    }


@app.get("/get-config")
async def fetch_config():
    try:
        return get_config()
    except Exception as e:
        return JSONResponse(status_code=500, content={"status": "error", "message": str(e)})


@app.post("/run-all")
async def run_all_modules(overrides: Dict[str, Any] = Body(default={})):
    try:
        if overrides:
            update_config(overrides)

        result = subprocess.run(
            [sys.executable, "run_all_modules.py"],
            capture_output=True,
            text=True
        )

        if result.returncode != 0:
            return JSONResponse(status_code=500, content={
                "status": "error",
                "message": "Pipeline execution failed",
                "stderr": result.stderr
            })

        return {
            "status": "success",
            "message": "Full pipeline executed successfully",
            "stdout": result.stdout
        }

    except Exception as e:
        return JSONResponse(status_code=500, content={"status": "error", "message": str(e)})


@app.post("/run/{module_name}")
async def run_single_module(module_name: str, overrides: Dict[str, Any] = Body(default={})):
    try:
        if module_name not in VALID_MODULES:
            return JSONResponse(status_code=400, content={
                "status": "error",
                "message": f"Invalid module name. Choose from: {VALID_MODULES}"
            })

        if overrides:
            update_config(overrides)

        result = subprocess.run(
            [sys.executable, "run_all_modules.py", module_name],
            capture_output=True,
            text=True
        )

        if result.returncode != 0:
            return JSONResponse(status_code=500, content={
                "status": "error",
                "message": f"{module_name} module failed",
                "stderr": result.stderr
            })

        return {
            "status": "success",
            "message": f"{module_name} module executed successfully",
            "stdout": result.stdout
        }

    except Exception as e:
        return JSONResponse(status_code=500, content={"status": "error", "message": str(e)})
