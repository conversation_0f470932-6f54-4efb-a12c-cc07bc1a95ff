apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: space-fastapi-ingress
  namespace: dll-space-optimization-ml
  annotations:
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "2500"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "2500"  
    nginx.ingress.kubernetes.io/proxy-read-timeout: "2500"
    nginx.ingress.kubernetes.io/proxy-body-size: "250m"
spec:
  ingressClassName: nginx
  rules:
    - host: lmdllcloudapp-dev.landmarkgroup.com
      http:
        paths:    
          - path: /space_openai/fastapi(/|$)(.*)
            pathType: Prefix
            backend:
              service:
                name: space-fastapi-backend-service
                port:
                  number: 80