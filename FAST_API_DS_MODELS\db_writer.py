import pandas as pd
from sqlalchemy import create_engine
from urllib.parse import quote_plus

# class DBWriter:
#     def __init__(self, cfg):
#         self.cfg = cfg
#         self.db_enabled = cfg.get("DATABASE_CONFIG", {}).get("ENABLED", False)
#         if self.db_enabled:
#             self.engine = self._get_engine()

#     def _get_engine(self):
#         db_type = self.cfg.get("DB_TYPE", "mysql")
#         user = self.cfg.get("DB_USER")
#         password = quote_plus(self.cfg.get("DB_PASSWORD", ""))
#         host = self.cfg.get("DB_HOST")
#         port = self.cfg.get("DB_PORT")
#         db_name = self.cfg.get("DB_NAME")

#         conn_str = f"{db_type}+pymysql://{user}:{password}@{host}:{port}/{db_name}"
#         return create_engine(conn_str)
    
    # def _create_engine(self):
    #     from sqlalchemy import create_engine
    #     from urllib.parse import quote_plus
    #     db_url = (
    #         f"mysql+pymysql://{self.cfg['DB_USER']}:{quote_plus(self.cfg['DB_PASSWORD'])}"
    #         f"@{self.cfg['DB_HOST']}:{self.cfg['DB_PORT']}/{self.cfg['DB_NAME']}"
    #     )
    #     return create_engine(db_url)
    # def write(self, df: pd.DataFrame, key: str):
    #     if not self.db_enabled:
    #         print("Database writing is disabled in config.")
    #         return

    #     if df is None or df.empty:
    #         print(f"No data to write for key: {key}")
    #         return

    #     table_name = self.cfg["DATABASE_CONFIG"]["RESULT_TABLES"].get(key)
    #     if not table_name:
    #         print(f"No table mapping found for key: {key}")
    #         return

    #     metadata = self.cfg["DATABASE_CONFIG"].get("DEFAULT_METADATA", {})
    #     resolved_metadata = {
    #         k: v.format(**self.cfg) if isinstance(v, str) else v
    #         for k, v in metadata.items()
    #     }

    #     try:
    #         # Add metadata columns
    #         for k, v in resolved_metadata.items():
    #             df.loc[:, k] = v

    #         # Clean up for MySQL compatibility
    #         if df.isin([float('inf'), float('-inf')]).any().any():
    #             print(f"DataFrame for key '{key}' contains 'inf' values. Replacing with NA.")

    #         df.replace([float('inf'), float('-inf')], pd.NA, inplace=True)
    #         df = df.where(pd.notnull(df), None)  # convert NA/NaN to None (MySQL-safe)

    #         write_mode = self.cfg.get("TABLE_WRITE_MODE", "replace")
    #         df.to_sql(table_name, self.engine, if_exists=write_mode, index=False)
    #         print(f"Data written to MySQL table: {table_name}")
    #     except Exception as e:
    #         print(f" Failed to write data to {table_name}: {e}")


#     def read(self, table_name: str) -> pd.DataFrame:
#         """Read an entire MySQL table into a DataFrame."""
#         if not self.engine:
#             raise ValueError("Database connection not available.")
#         query = f"SELECT * FROM {table_name}"
#         return pd.read_sql(query, self.engine)


import pandas as pd
from sqlalchemy import create_engine, text
from urllib.parse import quote_plus
from datetime import datetime

class DBWriter:
    def __init__(self, cfg):
        self.cfg = cfg
        self.db_enabled = self.cfg.get("ENABLE_DATABASE", True)  # Default to True
        self.engine = self._create_engine() if self.db_enabled else None

    def _create_engine(self):
        db_url = (
            f"mysql+pymysql://{self.cfg['DB_USER']}:{quote_plus(self.cfg['DB_PASSWORD'])}"
            f"@{self.cfg['DB_HOST']}:{self.cfg['DB_PORT']}/{self.cfg['DB_NAME']}"
        )
        
        return create_engine(db_url,echo=False, future=True)

    def write(self, df: pd.DataFrame, key: str):
        if not self.db_enabled:
            print("Database writing is disabled in config.")
            return

        if df is None or df.empty:
            print(f"No data to write for key: {key}")
            return

        table_name = self.cfg["DATABASE_CONFIG"]["RESULT_TABLES"].get(key)
        if not table_name:
            print(f"No table mapping found for key: {key}")
            return

        metadata = self.cfg["DATABASE_CONFIG"].get("DEFAULT_METADATA", {})
        resolved_metadata = {
            k: v.format(**self.cfg) if isinstance(v, str) else v
            for k, v in metadata.items()
        }

        try:
            # Add metadata columns
            for k, v in resolved_metadata.items():
                df.loc[:, k] = v

            # Add a single timestamp for the whole batch
            batch_timestamp = datetime.now()
            df.loc[:, "created_at"] = batch_timestamp  # column name can be changed if needed

            # # Clean up for MySQL compatibility
            # if df.isin([float('inf'), float('-inf')]).any().any():
            #     print(f"DataFrame for key '{key}' contains 'inf' values. Replacing with NA.")

            df = df.replace([float('inf'), float('-inf')], pd.NA)
            # df.replace([float('inf'), float('-inf')], pd.NA, inplace=True)
            df = df.where(pd.notnull(df), None)  # convert NA/NaN to None (MySQL-safe)

            write_mode = self.cfg.get("TABLE_WRITE_MODE", "replace")
            df.to_sql(table_name, self.engine, if_exists=write_mode, index=False)

            print(f"Data written to MySQL table: {table_name} at {batch_timestamp}")
        except Exception as e:
            print(f"Failed to write data to {table_name}: {e}")


    def read(self, table_name, filters=None, or_filters=None, latest_by=None, latest_only=False):
        """
        Read from MySQL table with advanced filtering.
        
        filters      = dict for AND conditions
        or_filters   = list of dicts for OR conditions
        latest_by    = column name to determine latest batch (e.g., timestamp column)
        latest_only  = if True, fetch only the latest batch
        
        Examples:
        filters = {"StoreID": 1001, "Quantity": (">", 50)}
        or_filters = [{"Country": "UAE"}, {"Country": "KSA"}]
        """
        query = f"SELECT * FROM {table_name}"
        where_clauses = []
        params = {}

        # AND Filters
        if filters:
            for idx, (col, condition) in enumerate(filters.items()):
                key = f"val_{idx}"
                if not isinstance(condition, tuple):
                    where_clauses.append(f"{col} = :{key}")
                    params[key] = condition
                else:
                    op = condition[0].lower()
                    if op == "between":
                        where_clauses.append(f"{col} BETWEEN :{key}_start AND :{key}_end")
                        params[f"{key}_start"] = condition[1]
                        params[f"{key}_end"] = condition[2]
                    elif op == "in":
                        placeholders = ", ".join([f":{key}_{i}" for i, _ in enumerate(condition[1])])
                        where_clauses.append(f"{col} IN ({placeholders})")
                        for i, val in enumerate(condition[1]):
                            params[f"{key}_{i}"] = val
                    elif op == "like":
                        where_clauses.append(f"{col} LIKE :{key}")
                        params[key] = condition[1]
                    else:  # >, <, >=, <=, !=
                        where_clauses.append(f"{col} {op} :{key}")
                        params[key] = condition[1]

        # OR Filters
        if or_filters:
            or_clauses = []
            for idx, or_filter in enumerate(or_filters):
                for col, condition in or_filter.items():
                    key = f"orval_{idx}"
                    if not isinstance(condition, tuple):
                        or_clauses.append(f"{col} = :{key}")
                        params[key] = condition
                    else:
                        op = condition[0].lower()
                        if op == "between":
                            or_clauses.append(f"{col} BETWEEN :{key}_start AND :{key}_end")
                            params[f"{key}_start"] = condition[1]
                            params[f"{key}_end"] = condition[2]
                        elif op == "in":
                            placeholders = ", ".join([f":{key}_{i}" for i, _ in enumerate(condition[1])])
                            or_clauses.append(f"{col} IN ({placeholders})")
                            for i, val in enumerate(condition[1]):
                                params[f"{key}_{i}"] = val
                        elif op == "like":
                            or_clauses.append(f"{col} LIKE :{key}")
                            params[key] = condition[1]
                        else:
                            or_clauses.append(f"{col} {op} :{key}")
                            params[key] = condition[1]
            if or_clauses:
                where_clauses.append("(" + " OR ".join(or_clauses) + ")")

        # # Add WHERE clauses if present
        # if where_clauses:
        #     query += " WHERE " + " AND ".join(where_clauses)

        # # If latest_only requested, wrap query to get only the max latest_by
        # if latest_only and latest_by:
        #     query += f" AND {latest_by} = (SELECT MAX({latest_by}) FROM {table_name})"


        # If latest_only requested, add condition to where_clauses first
        if latest_only and latest_by:
            where_clauses.append(
                f"{latest_by} = (SELECT MAX({latest_by}) FROM {table_name})"
            )

        # Add WHERE clauses if present
        if where_clauses:
            query += " WHERE " + " AND ".join(where_clauses)

        # Execute query
        return pd.read_sql(text(query), self.engine, params=params)

