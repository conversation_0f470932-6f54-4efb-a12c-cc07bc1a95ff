trigger:
- development-cicd

resources:
- repo: self

variables:
  imageRepo: space/space-fastapi-backend-img
  tag: '$(Build.BuildId)'

stages:
- stage: Build
  displayName: Build image
  jobs:
  - job: Build
    displayName: Build
    pool:
      name: app-self-hosted
    steps:
    - task: Docker@2
      displayName: Build an image
      inputs:
        containerRegistry: 'ACR-DLL-PRD01-DLL04_02'
        repository: '$(imageRepo)'
        command: 'buildAndPush'
        Dockerfile: '$(Build.SourcesDirectory)/DockerFile'
        tags: '$(tag)'
          
    - task: PublishPipelineArtifact@1
      inputs:
        targetPath: '$(Pipeline.Workspace)/s/manifests'
        artifact: 'manifests'
        publishLocation: 'pipeline'
        
- stage: Deploy
  displayName: Deploy to AKS Prod
  dependsOn: Build
  variables:
    acrsecret: k8sacrauth
    acrprodurl: 'lmapaz1acrdllprd02.azurecr.io'
  jobs:
  - job: Deploy
    displayName: Deploy to AKS
    pool:
      name: app-self-hosted
    steps:
    - task: DownloadPipelineArtifact@2
      inputs:
        buildType: 'current'
        artifactName: 'manifests'
        targetPath: '$(Pipeline.Workspace)/manifests'
    - task: KubernetesManifest@1
      inputs:
        action: 'createSecret'
        connectionType: 'azureResourceManager'
        azureSubscriptionConnection: 'SP-DLL-PRD01-DLL04'
        azureResourceGroup: 'RG_AZ1_PRD_DLL_ANL_02'
        kubernetesCluster: 'LMAPAZ1AKSPRDDLL03'
        useClusterAdmin: true
        namespace: 'dll-space-optimization-ml'
        secretType: 'dockerRegistry'
        secretName: '$(acrsecret)'
        dockerRegistryEndpoint: 'ACR-DLL-PRD01-DLL04_02'
    - task: KubernetesManifest@1
      inputs:
        action: 'deploy'
        connectionType: 'azureResourceManager'
        azureSubscriptionConnection: 'SP-DLL-PRD01-DLL04'
        azureResourceGroup: 'RG_AZ1_PRD_DLL_ANL_02'
        kubernetesCluster: 'LMAPAZ1AKSPRDDLL03'
        useClusterAdmin: true
        namespace: 'dll-space-optimization-ml'
        manifests: |
          $(Pipeline.Workspace)/s/manifests/space-fastapi-configmap.yml
          $(Pipeline.Workspace)/s/manifests/space-fastapi-deployment.yml
          $(Pipeline.Workspace)/s/manifests/space-fastapi-ingress.yml
          $(Pipeline.Workspace)/s/manifests/space-fastapi-secretprovider.yml
          $(Pipeline.Workspace)/s/manifests/space-fastapi-service.yml
        containers: '$(acrprodurl)/$(imageRepo):$(tag)'
    
    - task: CmdLine@2
      displayName: Removing the image generated
      inputs:
        script: 'docker rmi -f $(acrprodurl)/$(imageRepo):$(tag)'
