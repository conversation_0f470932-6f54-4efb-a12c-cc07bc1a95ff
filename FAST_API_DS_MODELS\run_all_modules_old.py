import subprocess
import os
import sys

def print_divider():
    print("\n" + "=" * 80 + "\n")

def run_module(module_name):
    print_divider()
    print(f"Running {module_name.upper()}...\n")

    process_file = os.path.join(module_name, 'process.py')

    try:
        result = subprocess.run(
            [sys.executable, process_file],
            capture_output=True,
            text=True,
            check=True
        )
        print(result.stdout)
        return {"module": module_name, "status": "success", "output": result.stdout}

    except subprocess.CalledProcessError as e:
        print(f"\n{module_name.upper()} FAILED:\n")
        print(e.stderr)
        return {"module": module_name, "status": "error", "error": e.stderr}

def run_all():
    modules = ['datapreparation', 'saturation', 'optimization']
    return [run_module(m) for m in modules]

if __name__ == "__main__":
    results = run_all()

    print_divider()
    print("SUMMARY REPORT\n" + "-" * 80)
    for r in results:
        print(f"{r['module']} --> {'SUCCESS' if r['status'] == 'success' else 'ERROR'}")
    print_divider()
